#!/bin/bash

# Laravel/Botble CMS Performance Optimization Script
# Dành cho Ubuntu 1 core 2GB RAM

echo "🚀 Bắt đầu tối ưu hiệu suất Laravel/Botble CMS..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "Không nên chạy script này với quyền root!"
   exit 1
fi

# Step 1: Clear existing cache
print_status "Bước 1: Xóa cache hiện tại..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan optimize:clear

# Step 2: Install Redis if not exists
print_status "Bước 2: Kiểm tra Redis..."
if ! command -v redis-server &> /dev/null; then
    print_warning "Redis chưa được cài đặt. Vui lòng cài đặt Redis:"
    echo "sudo apt update && sudo apt install redis-server"
    echo "sudo systemctl start redis-server"
    echo "sudo systemctl enable redis-server"
else
    print_status "Redis đã được cài đặt ✓"
fi

# Step 3: Update .env for cache optimization
print_status "Bước 3: Cập nhật cấu hình cache..."

# Backup .env file
cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
print_status "Đã backup file .env"

# Update cache settings in .env
if grep -q "CACHE_STORE=" .env; then
    sed -i 's/CACHE_STORE=.*/CACHE_STORE=redis/' .env
else
    echo "CACHE_STORE=redis" >> .env
fi

if grep -q "SESSION_DRIVER=" .env; then
    sed -i 's/SESSION_DRIVER=.*/SESSION_DRIVER=redis/' .env
else
    echo "SESSION_DRIVER=redis" >> .env
fi

if grep -q "QUEUE_CONNECTION=" .env; then
    sed -i 's/QUEUE_CONNECTION=.*/QUEUE_CONNECTION=redis/' .env
else
    echo "QUEUE_CONNECTION=redis" >> .env
fi

# Add Redis configuration if not exists
if ! grep -q "REDIS_HOST=" .env; then
    echo "" >> .env
    echo "# Redis Configuration" >> .env
    echo "REDIS_HOST=127.0.0.1" >> .env
    echo "REDIS_PASSWORD=null" >> .env
    echo "REDIS_PORT=6379" >> .env
    echo "REDIS_CLIENT=predis" >> .env
fi

print_status "Đã cập nhật cấu hình cache trong .env"

# Step 4: Generate optimized cache
print_status "Bước 4: Tạo cache tối ưu..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Step 5: Botble specific optimizations
print_status "Bước 5: Tối ưu Botble CMS..."
if php artisan list | grep -q "cms:cache:clear"; then
    php artisan cms:cache:clear
    print_status "Đã xóa cache CMS"
fi

# Step 6: General Laravel optimization
print_status "Bước 6: Tối ưu Laravel..."
php artisan optimize

# Step 7: Check PHP OPcache
print_status "Bước 7: Kiểm tra PHP OPcache..."
php -m | grep -i opcache > /dev/null
if [ $? -eq 0 ]; then
    print_status "PHP OPcache đã được kích hoạt ✓"
else
    print_warning "PHP OPcache chưa được kích hoạt. Vui lòng cấu hình:"
    echo "Thêm vào php.ini:"
    echo "opcache.enable=1"
    echo "opcache.memory_consumption=256"
    echo "opcache.interned_strings_buffer=16"
    echo "opcache.max_accelerated_files=10000"
    echo "opcache.validate_timestamps=0"
fi

# Step 8: Check file permissions
print_status "Bước 8: Kiểm tra quyền file..."
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache 2>/dev/null || true

# Step 9: Database optimization suggestions
print_status "Bước 9: Gợi ý tối ưu database..."
echo "Để tối ưu database, hãy chạy:"
echo "php artisan migrate:status"
echo "php artisan db:monitor (nếu có)"

# Step 10: Final checks
print_status "Bước 10: Kiểm tra cuối cùng..."

# Check if Redis is working
if redis-cli ping > /dev/null 2>&1; then
    print_status "Redis đang hoạt động ✓"
else
    print_warning "Redis không phản hồi. Vui lòng kiểm tra dịch vụ Redis"
fi

# Check cache status
if [ -f "bootstrap/cache/config.php" ]; then
    print_status "Config cache đã được tạo ✓"
fi

if [ -f "bootstrap/cache/routes-v7.php" ]; then
    print_status "Route cache đã được tạo ✓"
fi

echo ""
print_status "🎉 Hoàn thành tối ưu hiệu suất!"
echo ""
echo "📋 Các bước tiếp theo:"
echo "1. Restart web server: sudo systemctl restart nginx php8.2-fpm"
echo "2. Test website performance"
echo "3. Monitor logs: tail -f storage/logs/laravel.log"
echo "4. Check Redis: redis-cli monitor"
echo ""
echo "📊 Để kiểm tra hiệu suất:"
echo "- Truy cập website và kiểm tra debug timeline"
echo "- Sử dụng tools như GTmetrix, PageSpeed Insights"
echo ""
print_warning "Lưu ý: Đã backup file .env gốc với tên .env.backup.YYYYMMDD_HHMMSS"
