# Hướng Dẫn Tối Ưu Hiệu Suất <PERSON>/Botble CMS

## 🔍 Phân Tích Timeline Hiện Tại
- Application (95.24%): 856ms ⚠️ **VẤN ĐỀ CHÍNH**
- Booting (4.76%): 42.76ms ✅ Chấp nhận được
- Routing (0.22%): 1.98ms ✅ Tốt
- Preparing Response (0.01%): 69μs ✅ Tốt

## 🚀 Giải Pháp Tối Ưu Theo <PERSON> Tự Ưu Tiên

### 1. T<PERSON><PERSON> Ưu Cache (Ưu tiên cao)

#### A. <PERSON><PERSON>u hình Redis Cache
```bash
# Cài đặt Redis
sudo apt update
sudo apt install redis-server

# Khởi động Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### B. Cập nhật .env
```env
# Cache Configuration
CACHE_STORE=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_CLIENT=predis
```

#### C<PERSON>ache Commands
```bash
# Tạo cache cho config, routes, views
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Cache cho Botble CMS
php artisan cms:cache:clear
php artisan optimize
```

### 2. Tối Ưu Database (Ưu tiên cao)

#### A. Cấu hình MySQL
```sql
-- Tối ưu MySQL cho server 1 core 2GB RAM
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
query_cache_size = 128M
query_cache_type = 1
max_connections = 50
thread_cache_size = 8
table_open_cache = 1000
```

#### B. Database Indexing
```bash
# Kiểm tra slow queries
php artisan db:monitor

# Tạo index cho các bảng thường dùng
```

### 3. Tối Ưu PHP (Ưu tiên cao)

#### A. Cấu hình PHP-FPM
```ini
; /etc/php/8.2/fpm/pool.d/www.conf
pm = dynamic
pm.max_children = 10
pm.start_servers = 2
pm.min_spare_servers = 1
pm.max_spare_servers = 3
pm.max_requests = 500

; /etc/php/8.2/fpm/php.ini
memory_limit = 512M
max_execution_time = 300
opcache.enable = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 10000
opcache.validate_timestamps = 0
opcache.save_comments = 1
opcache.fast_shutdown = 1
```

#### B. Restart Services
```bash
sudo systemctl restart php8.2-fpm
sudo systemctl restart nginx
```

### 4. Tối Ưu Botble CMS Settings

#### A. Enable Cache trong Admin
- Vào Admin Panel → Settings → Cache
- Bật tất cả cache options:
  - Cache Admin Menu: ON
  - Cache Site Map: ON
  - Cache Front Menu: ON
  - Cache User Avatar: ON

#### B. Optimize Package Settings
- Vào Admin Panel → Settings → Optimize
- Bật các tùy chọn:
  - Collapse Whitespace: ON
  - Remove Comments: ON
  - Inline CSS: ON (nếu cần)

### 5. Server Configuration (Ubuntu)

#### A. Nginx Configuration
```nginx
# /etc/nginx/sites-available/your-site
server {
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
    
    # Browser caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # PHP-FPM configuration
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }
}
```

### 6. Monitoring & Debug

#### A. Enable Query Logging (Temporary)
```env
# Trong .env để debug
DB_LOG_QUERIES=true
LOG_LEVEL=debug
```

#### B. Performance Monitoring Commands
```bash
# Kiểm tra slow queries
php artisan telescope:install  # Nếu dùng Telescope

# Monitor cache hit rate
redis-cli info stats

# Check PHP processes
ps aux | grep php-fpm
```

## 📈 Kết Quả Mong Đợi

Sau khi áp dụng các tối ưu trên:
- **Application time**: Giảm từ 856ms xuống ~200-300ms
- **Total response time**: Giảm từ ~900ms xuống ~250-350ms
- **Memory usage**: Giảm đáng kể
- **Database queries**: Nhanh hơn 3-5 lần

## ⚠️ Lưu Ý Quan Trọng

1. **Backup trước khi thay đổi**
2. **Test trên staging environment trước**
3. **Monitor sau khi deploy**
4. **Áp dụng từng bước một để dễ debug**

## 🔧 Commands Cần Chạy Ngay

```bash
# Bước 1: Clear cache hiện tại
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Bước 2: Tạo cache mới
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Bước 3: Optimize
php artisan optimize
```
