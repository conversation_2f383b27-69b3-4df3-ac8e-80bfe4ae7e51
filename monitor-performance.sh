#!/bin/bash

# Performance Monitoring Script for Laravel/Botble CMS
# Kiểm tra hiệu suất sau khi tối ưu

echo "📊 Kiểm tra hiệu suất Laravel/Botble CMS..."

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_header() {
    echo -e "\n${GREEN}=== $1 ===${NC}"
}

print_info() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# 1. Check PHP OPcache Status
print_header "PHP OPcache Status"
php -r "
if (function_exists('opcache_get_status')) {
    \$status = opcache_get_status();
    if (\$status['opcache_enabled']) {
        echo '✓ OPcache: Enabled\n';
        echo '  Memory Used: ' . round(\$status['memory_usage']['used_memory']/1024/1024, 2) . 'MB\n';
        echo '  Hit Rate: ' . round(\$status['opcache_statistics']['opcache_hit_rate'], 2) . '%\n';
        echo '  Cached Files: ' . \$status['opcache_statistics']['num_cached_scripts'] . '\n';
    } else {
        echo '✗ OPcache: Disabled\n';
    }
} else {
    echo '✗ OPcache: Not Available\n';
}
"

# 2. Check Redis Status
print_header "Redis Status"
if command -v redis-cli &> /dev/null; then
    if redis-cli ping > /dev/null 2>&1; then
        print_info "Redis: Connected"
        echo "  Memory Usage: $(redis-cli info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')"
        echo "  Connected Clients: $(redis-cli info clients | grep connected_clients | cut -d: -f2 | tr -d '\r')"
        echo "  Total Commands: $(redis-cli info stats | grep total_commands_processed | cut -d: -f2 | tr -d '\r')"
    else
        print_error "Redis: Not responding"
    fi
else
    print_error "Redis: Not installed"
fi

# 3. Check Laravel Cache Status
print_header "Laravel Cache Status"
if [ -f "bootstrap/cache/config.php" ]; then
    print_info "Config Cache: Enabled"
else
    print_warning "Config Cache: Disabled"
fi

if [ -f "bootstrap/cache/routes-v7.php" ]; then
    print_info "Route Cache: Enabled"
else
    print_warning "Route Cache: Disabled"
fi

if [ -d "storage/framework/views" ] && [ "$(ls -A storage/framework/views)" ]; then
    print_info "View Cache: Has cached views"
else
    print_warning "View Cache: No cached views"
fi

# 4. Check File Permissions
print_header "File Permissions"
if [ -w "storage" ] && [ -w "bootstrap/cache" ]; then
    print_info "Storage & Cache directories: Writable"
else
    print_error "Storage or Cache directories: Not writable"
fi

# 5. Check Environment Configuration
print_header "Environment Configuration"
if grep -q "APP_DEBUG=false" .env; then
    print_info "Debug Mode: Disabled (Production)"
elif grep -q "APP_DEBUG=true" .env; then
    print_warning "Debug Mode: Enabled (Development)"
else
    print_warning "Debug Mode: Not set"
fi

if grep -q "CACHE_STORE=redis" .env; then
    print_info "Cache Driver: Redis"
elif grep -q "CACHE_STORE=file" .env; then
    print_warning "Cache Driver: File (Consider Redis)"
else
    print_warning "Cache Driver: Not configured"
fi

if grep -q "SESSION_DRIVER=redis" .env; then
    print_info "Session Driver: Redis"
elif grep -q "SESSION_DRIVER=file" .env; then
    print_warning "Session Driver: File (Consider Redis)"
else
    print_warning "Session Driver: Not configured"
fi

# 6. Check System Resources
print_header "System Resources"
echo "Memory Usage:"
free -h | grep -E "Mem|Swap"

echo -e "\nDisk Usage:"
df -h | grep -E "/$|/var"

echo -e "\nCPU Load:"
uptime

# 7. Check PHP-FPM Status
print_header "PHP-FPM Status"
if systemctl is-active --quiet php8.2-fpm; then
    print_info "PHP-FPM: Running"
    echo "  Active Processes: $(ps aux | grep php-fpm | grep -v grep | wc -l)"
else
    print_error "PHP-FPM: Not running"
fi

# 8. Check Nginx Status
print_header "Nginx Status"
if systemctl is-active --quiet nginx; then
    print_info "Nginx: Running"
else
    print_error "Nginx: Not running"
fi

# 9. Performance Test
print_header "Quick Performance Test"
echo "Testing response time..."
if command -v curl &> /dev/null; then
    RESPONSE_TIME=$(curl -o /dev/null -s -w "%{time_total}" http://localhost 2>/dev/null || echo "N/A")
    if [ "$RESPONSE_TIME" != "N/A" ]; then
        echo "Response Time: ${RESPONSE_TIME}s"
        if (( $(echo "$RESPONSE_TIME < 1.0" | bc -l) )); then
            print_info "Response time is good (< 1s)"
        elif (( $(echo "$RESPONSE_TIME < 3.0" | bc -l) )); then
            print_warning "Response time is acceptable (1-3s)"
        else
            print_error "Response time is slow (> 3s)"
        fi
    else
        print_warning "Could not test response time"
    fi
else
    print_warning "curl not available for testing"
fi

# 10. Recommendations
print_header "Recommendations"
echo "📋 Để cải thiện hiệu suất thêm:"
echo "1. Kiểm tra slow query logs: tail -f /var/log/mysql/slow.log"
echo "2. Monitor Redis: redis-cli monitor"
echo "3. Check Laravel logs: tail -f storage/logs/laravel.log"
echo "4. Use tools: GTmetrix, PageSpeed Insights"
echo "5. Consider CDN for static assets"

echo -e "\n🔧 Useful commands:"
echo "- Clear all cache: php artisan optimize:clear"
echo "- Rebuild cache: php artisan optimize"
echo "- Check queue: php artisan queue:work --daemon"
echo "- Monitor processes: htop"

echo -e "\n✅ Monitoring completed!"
