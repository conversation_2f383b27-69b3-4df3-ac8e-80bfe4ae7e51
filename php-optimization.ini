; PHP Optimization Configuration for Laravel/Botble CMS
; Dành cho Ubuntu 1 core 2GB RAM
; Copy nội dung này vào /etc/php/8.2/fpm/php.ini và /etc/php/8.2/cli/php.ini

[PHP]
; Memory Settings
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
max_input_vars = 3000

; File Upload Settings
upload_max_filesize = 64M
post_max_size = 64M
file_uploads = On

; Error Reporting (Production)
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php_errors.log

; Session Settings
session.gc_maxlifetime = 7200
session.gc_probability = 1
session.gc_divisor = 100

; OPcache Settings (Quan trọng nhất)
[opcache]
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 10000
opcache.max_wasted_percentage = 5
opcache.use_cwd = 1
opcache.validate_timestamps = 0
opcache.revalidate_freq = 0
opcache.save_comments = 1
opcache.fast_shutdown = 1
opcache.enable_file_override = 0
opcache.optimization_level = 0x7FFFBFFF
opcache.inherited_hack = 1
opcache.dups_fix = 0
opcache.blacklist_filename = ""

; Realpath Cache (Tăng hiệu suất file system)
realpath_cache_size = 4096K
realpath_cache_ttl = 600

; Other Performance Settings
expose_php = Off
allow_url_fopen = On
allow_url_include = Off
default_socket_timeout = 60

; Date Settings
date.timezone = "Asia/Ho_Chi_Minh"

; Extensions (Đảm bảo các extension cần thiết được load)
extension=curl
extension=gd
extension=json
extension=mbstring
extension=mysqli
extension=pdo_mysql
extension=redis
extension=zip
extension=xml
extension=intl
