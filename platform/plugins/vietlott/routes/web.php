<?php

use Bo<PERSON>ble\Base\Facades\AdminHelper;
use Illuminate\Support\Facades\Route;
use Plugin\Vietlott\Http\Controllers\Bingo18Controller;
use Plugin\Vietlott\Http\Controllers\KenoController;
use Plugin\Vietlott\Http\Controllers\Max3DController;
use Plugin\Vietlott\Http\Controllers\Max3DProController;
use Plugin\Vietlott\Http\Controllers\Power535Controller;
use Plugin\Vietlott\Http\Controllers\Power645Controller;
use Plugin\Vietlott\Http\Controllers\Power655Controller;
use Plugin\Vietlott\Http\Controllers\Settings\VietlottSettingController;
use Plugin\Vietlott\Http\Controllers\VietlottRetryNumberController;

AdminHelper::registerRoutes(function () {
    Route::group([
        'prefix' => 'vietlott',
        'as' => 'vietlott.',
    ], function () {
        // Retry Number
        Route::group([
            'prefix' => 'retry-number',
            'as' => 'retry-number',
            'controller' => VietlottRetryNumberController::class,
        ], function () {
            Route::get('', 'edit');
            Route::post('', 'update')->name('.update');
            Route::get('/page-info', 'pageInfo')->name('.page-info');
        });
        // Keno
        Route::group([
            'prefix' => 'keno',
            'as' => 'keno.',
        ], function () {
            Route::resource('', KenoController::class)
                ->parameters(['' => 'draw'])
                ->only(['index']);
            
            Route::get('live-data', [KenoController::class, 'liveData'])
                ->name('live-data');
            
            Route::get('draw-html', [KenoController::class, 'getDrawHtml'])
                ->name('draw-html');
        });

        // Power 6/55
        Route::group([
            'prefix' => 'power655',
            'as' => 'power655.',
        ], function () {
            Route::resource('', Power655Controller::class)
                ->parameters(['' => 'draw'])
                ->only(['index']);
        });

        // Mega 6/45
        Route::group([
            'prefix' => 'power645',
            'as' => 'power645.',
        ], function () {
            Route::resource('', Power645Controller::class)
                ->parameters(['' => 'draw'])
                ->only(['index']);
        });

        // Power 5/35
        Route::group([
            'prefix' => 'power535',
            'as' => 'power535.',
        ], function () {
            Route::resource('', Power535Controller::class)
                ->parameters(['' => 'draw'])
                ->only(['index']);
        });

        // Max 3D
        Route::group([
            'prefix' => 'max3d',
            'as' => 'max3d.',
        ], function () {
            Route::resource('', Max3DController::class)
                ->parameters(['' => 'draw'])
                ->only(['index']);
        });

        // Max 3D Pro
        Route::group([
            'prefix' => 'max3dpro',
            'as' => 'max3dpro.',
        ], function () {
            Route::resource('', Max3DProController::class)
                ->parameters(['' => 'draw'])
                ->only(['index']);
        });

        // Bingo 18
        Route::group([
            'prefix' => 'bingo18',
            'as' => 'bingo18.',
        ], function () {
            Route::resource('', Bingo18Controller::class)
                ->parameters(['' => 'draw'])
                ->only(['index']);
        });
    });

    Route::group([
        'prefix' => 'settings/vietlott',
        'as' => 'vietlott.settings',
        'controller' => VietlottSettingController::class,
    ], function (): void {
        Route::get('', 'edit');
        Route::put('', 'update')->name('.update');
    });
});
