<?php

use Botble\Base\Facades\AdminHelper;
use Illuminate\Support\Facades\Route;
use Plugin\Proxy\Http\Controllers\ProxyController;

AdminHelper::registerRoutes(function (): void {
    Route::group([
        'prefix' => 'proxies',
        'as' => 'proxies.',
        'controller' => ProxyController::class,
    ], function () {
        Route::resource('', ProxyController::class)
            ->parameters(['' => 'proxy']);

        Route::get('import', [
            'as' => 'import',
            'uses' => 'getImport',
            'permission' => 'proxies.edit',
        ]);

        Route::post('import', [
            'as' => 'import.post',
            'uses' => 'postImport',
            'permission' => 'proxies.edit',
        ]);
    });
});
