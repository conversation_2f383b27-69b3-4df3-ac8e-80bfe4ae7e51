<?php

namespace Plugin\Lottery\Services;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Plugin\Lottery\Enums\LotteryRegionEnum;
use Plugin\Lottery\Facades\LotterySupport;
use Plugin\Lottery\Models\LotteryCityDate;
use Plugin\Lottery\Models\LotteryResult;
use Plugin\Lottery\Services\Traits\CrawlTrait;
use Symfony\Component\DomCrawler\Crawler;

class LotteryLiveService
{
    use CrawlTrait;

    protected string $region;

    protected string $paramRegion;

    protected ?LotteryResult $model;

    protected ?Collection $cities;

    protected bool $debug = false;

    public function __construct()
    {
        $this->baseUrl = 'https://data.minhngoc.net/O0O/0/';

        $this->sprintfUrl = $this->baseUrl . '%s/%s.html';
    }

    public static function make(): static
    {
        return app(self::class);
    }

    public function setRegion(string $region): static
    {
        $this->region = $region;
        $this->setParamRegion($this->region);
        $this->setModel($this->region);

        return $this;
    }

    public function setModel(string $region): static
    {
        $this->model = app(LotteryRegionEnum::getModel($region));

        return $this;
    }

    public function setParamRegion(string $region): static
    {
        $this->paramRegion = LotteryRegionEnum::getCrawlParam($region);

        return $this;
    }

    public function run()
    {
        $this->init2();

        $this->cities = LotterySupport::getCities();

        switch ($this->region) {
            case LotteryRegionEnum::MB:
                $str = $this->liveMienBac();

                break;

            case LotteryRegionEnum::MT:
                $str = $this->liveMienTrung();

                break;
            case LotteryRegionEnum::MN:
                $str = $this->liveMienNam();

                break;
            default:
                throw new Exception('Error Processing Request', 1);

                break;
        }

        // $str = setting('lottery_live_' . $this->region . '_data');
        // dd($str);
        if (stripos($str, 'run:1') || stripos($str, 'run:0')) {
            Cache::set('lottery_live.' . $this->region . '.data', $str, 5 * 60 * 60);
            // Cache::set('lottery_live.' . $this->region . '.time', $str);

            if (stripos($str, 'run:1')) {
                if (preg_match("/(\*|\+)/m", $str)) {

                } else {
                    $maxDateKey = 'lottery_live.' . $this->region . '.max_date';
                    $maxDate = Cache::get($maxDateKey);
                    $today = Carbon::today()->toDateString();

                    // if ($maxDate != $today) {
                    if (true) {
                        $str = LotterySupport::convertToArray($str);
                        $items = json_decode($str, true);

                        $numberResults = LotterySupport::parseNumber($items['kq'], $this->region);

                        // dd($numberResults);
                        $model = LotteryRegionEnum::getModel($this->region);

                        $this->storeToModel($model, $numberResults, $today);

                        if ($this->region == LotteryRegionEnum::MB) {
                            $numberResults = LotterySupport::parseDienToanNumber($items['dt']);
                            $model = LotteryRegionEnum::getModel(LotteryRegionEnum::DT);
                            $this->storeToModel($model, $numberResults, $today);
                        }

                        Cache::set($maxDateKey, $today, Carbon::now()->endOfDay());
                    }
                }
            }
        }

        return true;
    }

    public function storeToModel($model, $numberResults, $date)
    {
        $cityDates = LotteryCityDate::query()
            ->whereIn('city_id', array_keys($numberResults))
            ->whereDate('date', $date)
            ->getQuery()
            ->get();
        foreach ($numberResults as $cityId => $numberResult) {
            $cityDate = $cityDates->where('city_id', $cityId)->firstWhere('date', $date);
            if (! $cityDate) {
                $cityDate = LotteryCityDate::query()
                    ->create([
                        'city_id' => $cityId,
                        'date' => $date,
                        'code' => Arr::get(collect($numberResult['results'])->firstWhere('level', 'code'), 'number'),
                    ]);

                $results = [];
                foreach ($numberResult['results'] as $item) {
                    if ($item['level'] == 'code') {
                        continue;
                    }

                    $results[] = [
                        ...$item,
                        'date' => $date,
                        'lottery_id' => $cityDate->id,
                    ];
                }
                app($model)->query()->insert($results);
            }
        }
    }

    public function liveMienTrung()
    {
        $url = $this->baseUrl . 'xstt/js_m3.js?_=' . time();

        $log_str = "MienTrung :: start - url: {$url}";
        $this->log($log_str);

        $str = $this->loadHTML($url);

        return $str;
    }

    public function liveMienNam()
    {
        $url = $this->baseUrl . 'xstt/js_m1.js?_=' . time();

        $log_str = "MienNam :: start - url: {$url}";
        $this->log($log_str);

        $str = $this->loadHTML($url);

        return $str;

    }

    public function liveMienBac()
    {
        $url = $this->baseUrl . 'xstt/js_m2.js?_=' . time();

        $log_str = "MienBac :: start - url: {$url}";
        $this->log($log_str);

        $str = $this->loadHTML($url);

        return $str;
        // dd($numbers);
    }

    private function loadHTML($url)
    {
        if ($this->debug) {
            if (strpos($url, 'js_m1')) {
                return 'kqxs.mn={run:1,tinh:"10,11,12",ntime:1508922047,delay:30000,kq:{10:{lv:"10K4",8:"63",7:"654",6:["8654","0955","7731"],5:"3851",4:["76259","65551","75213","68658","88779","46225","14531"],3:["96387","84766"],2:"61212",1:"65592",0:"330390"},11:{lv:"K4T10",8:"49",7:"535",6:["0058","4463","4160"],5:"2243",4:["61580","28263","32058","53248","59987","57283","00063"],3:["26650","42617"],2:"34556",1:"93594",0:"112973"},12:{lv:"K4T10",8:"87",7:"479",6:["9656","7249","9913"],5:"4764",4:["93292","41978","42118","58862","44046","04539","69956"],3:["81076","30213"],2:"21006",1:"24706",0:"418962"}}};';
            } elseif (strpos($url, 'js_m3')) {
                return 'kqxs.mt={run:1,tinh:"30,31",ntime:1508925750,delay:3000,kq:{30:{lv:"",8:"02",7:"+++",6:["****","****","****"],5:"****",4:["*****","*****","*****","*****","*****","*****","*****"],3:["*****","12345"],2:"*****",1:"*****",0:"545342"},31:{lv:"",8:"30",7:"***",6:["****","****","****"],5:"****",4:["*****","*****","*****","*****","*****","*****","*****"],3:["*****","*****"],2:"*****",1:"*****",0:"******"}}};';
            } else {
                return 'kqxs.mb={run:1,tinh:"48",ntime:1509533924,delay:30000,kq:{48:{lv:"8EC-1EC-6EC",7:["30","70","25","79"],6:["686","923","168"],5:["2184","2233","7724","2276","5577","4672"],4:["8273","2241","0754","3526"],3:["14294","70309","99370","77709","96782","98851"],2:["74405","78560"],1:"55355",0:"83492"}},dt:{d6x36:{run:1,kq:["08","13","20","23","24","28"]},d123:{run:1,kq:["4","25","000"]},dtt4:{run:1,kq:["6307"]},d6x45:{run:1,kq:{id:6,g:["04","12","25","39","42","44"]}}}};';
            }
        }

        $this->log('curl load: ' . $url);

        $crawler = $this->httpClient->get($url);

        $result = $crawler->body();

        return $result;
    }

    private function log($data)
    {
        return;
        $data = date('Y-m-d H:i:s :: ') . $data . ' - url: ' . "\n";
        File::ensureDirectoryExists(storage_path('logs/region-live/' . date('Y-m-d')));
        file_put_contents(storage_path('logs/region-live/' . date('Y-m-d') . '/truc-tiep-log.txt'), $data, FILE_APPEND);
    }

    protected function parseHTML(Crawler $crawler)
    {
        $items = [];

        $boxes = $crawler->filter('table.bkqmiennam');

        $boxes->each(function (Crawler $box) use (&$items) {
            $itemRow = [];

            $tds = $box->filter('tbody > tr > td');

            $date = trim($box->filter('td.ngay')->text());
            $itemRow['date'] = Carbon::createFromFormat('d/m/Y', $date)->format('Y-m-d');

            $box
                ->children('tbody tr')
                ->each(function (Crawler $ketqua) use (&$itemRow) {
                    $ketqua
                        ->filter('table.rightcl')
                        ->each(function (Crawler $tableNode) use (&$itemRow) {
                            $matinhNode = $tableNode->filter('td.matinh')->text();
                            $explode = explode('-', $matinhNode, 2);
                            $cityCode = trim(Arr::first($explode));

                            $cityId = $this->cities->firstWhere('code', $cityCode)?->getKey();

                            $itemRow['prizes'][$cityId] = $this->getPrizeNumber($tableNode);
                        });
                });

            $items[] = $itemRow;
        });

        return $items;
    }

    protected function parseHTMLDienToan(Crawler $crawler)
    {
        $items = [];

        $boxes = $crawler->filter('div.boxkqxsdientoan');

        $boxes->each(function (Crawler $box) use (&$items) {
            $itemRow = [];

            $dateDom = $box->filter('h4 a');
            $date = trim($dateDom->eq(1)->text());
            $itemRow['date'] = Carbon::createFromFormat('d/m/Y', $date)->format('Y-m-d');

            $cities = $this->cities->where('region', $this->region)->where('parent_id');

            foreach ($cities as $city) {
                $bkq123 = $box->filter($city->minhNgocClass);

                if ($bkq123->count() != 1) {
                    $element = $bkq123
                        ->reduce(function (Crawler $node) use ($city) {
                            return Str::endsWith($node->filter('.title .title')->text(), $city->minhNgocTitle);
                        })
                        ->first();
                } else {
                    $element = $bkq123->first();
                }

                switch ($city->code) {
                    case 'dientoan123':
                        $elements = $element->filter('.result-number > *');

                        // Mảng chứa kết quả
                        $groupedNumbers = '';

                        $elements->each(function (Crawler $node) use (&$groupedNumbers) {
                            if ($node->nodeName() === 'div') {
                                // Nếu là <div>, lấy nội dung số
                                $groupedNumbers .= trim($node->text());
                            } elseif ($node->nodeName() === 'li') {
                                $groupedNumbers .= ';';
                            }
                        });
                        $itemRow['prizes'][$city->id] = [
                            'dientoan' => [$groupedNumbers],
                        ];

                        break;
                    case 'thantai4':
                    case 'dientoan6x36':
                        $elements = $element->filter('.result-number > *');

                        // Mảng chứa kết quả
                        $groupedNumbers = [];

                        $elements->each(function ($node) use (&$groupedNumbers) {
                            $groupedNumbers[] = $node->text();
                        });

                        $itemRow['prizes'][$city->id] = [
                            'dientoan' => array_filter([implode(';', $groupedNumbers)]),
                        ];

                        break;
                    default:
                        # code...
                        break;
                }
            }

            $items[] = $itemRow;
        });

        return $items;
    }

    protected function getPrizeNumber(Crawler $xpath)
    {
        $prizeEnums = LotteryRegionEnum::getPrizes($this->region);

        $prizes = [];
        foreach ($prizeEnums as $key => $class) {
            $domList = $xpath->filter('td.' . $class . ' div');
            $items = [];
            $domList->each(function ($item) use (&$items) {
                $items[] = trim($item->text());
            });

            $prizes[$key] = $items;
        }

        return $prizes;
    }
}
